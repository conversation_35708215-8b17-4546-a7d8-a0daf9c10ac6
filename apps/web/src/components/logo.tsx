import Image from "next/image"
import Link from "next/link"

interface LogoProps {
  size?: number
  className?: string
  href?: string
  showText?: boolean
}

export default function Logo({ 
  size = 160, // Double the size - even MORE prominent!
  className = "", 
  href = "/dashboard",
  showText = false // Not needed since Just-Logo.svg is standalone
}: LogoProps) {
  const logoElement = (
    <div className={`flex items-center ${className}`}>
      <Image
        src="/Just-Logo.svg"
        alt="BuddyChip Logo"
        width={size}
        height={size}
        className="flex-shrink-0 hover:scale-110 transition-transform duration-200"
        priority
      />
    </div>
  )

  if (href) {
    return (
      <Link href={href} className="hover:opacity-90 transition-opacity">
        {logoElement}
      </Link>
    )
  }

  return logoElement
}