"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { useUser } from '@clerk/nextjs';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { useState } from "react";
import IconButton from "../atoms/icon-button";
import { RiTwitterXLine } from "react-icons/ri";
import Logo from "@/components/logo";

export function Navigation() {
  const { user, isLoaded } = useUser();

  return (
    <nav className="w-full z-50 flex justify-between items-center px-4 md:px-6 h-[60px] sm:h-[66px] md:h-[104px] bg-app-background">
      <div className="flex items-center">
        <Logo size={160} href="/" showText={false} />
      </div>
      
      <div className="flex items-center gap-1 sm:gap-2">
        {/* Social Icons */}
        <IconButton 
          icon={RiTwitterXLine} 
          onClick={() => window.open('https://x.com/BuddychipAI', '_blank')} 
          className="hover:bg-app-main hover:text-app-secondary w-8 h-8 sm:w-10 sm:h-10" 
        />
        
        {!user && isLoaded && (
          <>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => window.open('https://t.me/+4n7CEX8GKApkMmQ0', '_blank')}
              className="border border-app-stroke bg-transparent text-app-headline hover:bg-app-stroke hover:text-app-secondary"
            >
              Access Beta!
            </Button>
            <Link href="/sign-in">
              <Button 
                size="sm"
                className="bg-app-main text-app-secondary hover:bg-app-highlight"
              >
                Sign In
              </Button>
            </Link>
          </>
        )}
        
        {user && isLoaded && (
          <>
            <Link href="/dashboard">
              <Button 
                size="sm"
                className="bg-app-main text-app-secondary hover:bg-app-highlight flex items-center space-x-1"
              >
                <span>Dashboard</span>
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  className="relative h-10 flex items-center gap-2 hover:bg-app-stroke"
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user?.imageUrl} alt={user?.fullName || 'User'} />
                    <AvatarFallback className="bg-app-main text-app-secondary">
                      {(user?.fullName || user?.firstName || 'U').charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-left">
                    <p className="text-sm font-medium text-app-headline">
                      {user?.fullName || user?.firstName || 'User'}
                    </p>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64 bg-app-card border-app-stroke">
                <div className="flex items-center gap-3 p-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={user?.imageUrl} alt={user?.fullName || 'User'} />
                    <AvatarFallback className="bg-app-main text-app-secondary">
                      {(user?.fullName || user?.firstName || 'U').charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium text-app-headline">
                      {user?.fullName || user?.firstName || 'User'}
                    </p>
                    <p className="text-xs text-app-headline opacity-70">
                      {user?.primaryEmailAddress?.emailAddress}
                    </p>
                  </div>
                </div>
                
                <DropdownMenuSeparator />
                
                <Link href="/sign-out">
                  <DropdownMenuItem className="text-app-headline hover:bg-app-main/10 cursor-pointer">
                    Sign Out
                  </DropdownMenuItem>
                </Link>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        )}
      </div>
    </nav>
  );
}
