import { withSentryConfig } from "@sentry/nextjs";
import type { NextConfig } from "next";

const nextConfig: NextConfig = {};

// Only use Sentry webpack plugin in production to avoid Turbopack conflicts
const finalConfig = process.env.NODE_ENV === "production" 
  ? withSentryConfig(nextConfig, {
      org: "francesco-oddo",
      project: "buddychip-web",
      silent: !process.env.CI,
      widenClientFileUpload: true,
      disableLogger: true,
      automaticVercelMonitors: true,
    })
  : nextConfig;

export default finalConfig;
